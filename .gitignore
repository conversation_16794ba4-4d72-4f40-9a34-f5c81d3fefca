# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/
apps/*/.next/
apps/*/out/

# production
/build
apps/*/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env
.env.local
.env.development
.env.test
.env.production
# Keep .env.example for reference - DO NOT ignore it

# vercel
.vercel

# turborepo
.turbo

# typescript
*.tsbuildinfo
next-env.d.ts
