# Turbopack Issue Solution - Encreasl Monorepo

## Problem Description

When running `pnpm run dev` in the `apps/web` directory with the `--turbopack` flag, the following error occurs:

```
⨯ [Error [TurbopackInternalError]: Failed to write page endpoint /_app

Caused by:
- FileSystemPath("").join("../C:\Users\<USER>\Desktop\encreasl\apps\web") leaves the filesystem root
```

## Root Cause

This is a **known bug in Turbopack** that affects:
- **Windows systems** 
- **Monorepo setups** (like Turborepo)
- **Next.js 15.4.0-canary.89 and later versions**

The issue was introduced in [PR #80683](https://github.com/vercel/next.js/pull/80683) and is tracked in:
- [GitHub Discussion #80750](https://github.com/vercel/next.js/discussions/80750)
- [GitHub Issue #81628](https://github.com/vercel/next.js/issues/81628)

## Solution Applied

### 1. Updated Next.js Configuration

Enhanced `apps/web/next.config.ts` with proper Turbopack configuration:

```typescript
import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  transpilePackages: ["@encreasl/ui"],
  turbopack: {
    resolveAlias: {
      "@/*": "./src/*",
    },
  },
};

export default nextConfig;
```

### 2. Modified Package Scripts

Updated `apps/web/package.json` to provide both options:

```json
{
  "scripts": {
    "build": "next build",
    "dev": "next dev",
    "dev:turbo": "next dev --turbopack",
    "lint": "next lint",
    "start": "next start",
    "type-check": "tsc --noEmit"
  }
}
```

### 3. Updated Turbo Configuration

Added the new script to `turbo.json`:

```json
{
  "tasks": {
    "dev": {
      "cache": false,
      "persistent": true
    },
    "dev:turbo": {
      "cache": false,
      "persistent": true
    }
  }
}
```

## Current Working Solution

**Use the regular webpack bundler instead of Turbopack:**

```bash
# From the apps/web directory
pnpm run dev

# Or from the root directory using Turbo
pnpm turbo run dev --filter=@encreasl/web
```

This runs Next.js with the standard webpack bundler, which works perfectly with the monorepo setup.

## Future Turbopack Usage

Once the Turbopack bug is fixed in a future Next.js release, you can use:

```bash
# From the apps/web directory
pnpm run dev:turbo

# Or from the root directory using Turbo
pnpm turbo run dev:turbo --filter=@encreasl/web
```

## Verification

✅ **Application Status**: Successfully running on http://localhost:3000
✅ **Compilation**: No errors with webpack bundler
✅ **UI Components**: @encreasl/ui package imports working correctly
✅ **Monorepo**: Turborepo workspace resolution functioning properly

## Monitoring for Fix

Keep an eye on:
- [Next.js releases](https://github.com/vercel/next.js/releases)
- [GitHub Issue #81628](https://github.com/vercel/next.js/issues/81628)

The Vercel team is aware of this issue and working on a fix for future releases.
