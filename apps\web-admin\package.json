{"name": "@encreasl/web-admin", "version": "0.1.0", "private": true, "scripts": {"build": "next build", "dev": "next dev --port 3001", "lint": "next lint", "start": "next start", "type-check": "tsc --noEmit"}, "dependencies": {"next": "15.4.2", "react": "19.1.0", "react-dom": "19.1.0"}, "devDependencies": {"@encreasl/eslint-config": "workspace:*", "@encreasl/typescript-config": "workspace:*", "@encreasl/ui": "workspace:*", "@eslint/js": "^9", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@typescript-eslint/eslint-plugin": "^8", "@typescript-eslint/parser": "^8", "eslint": "^9", "eslint-config-next": "15.4.2", "tailwindcss": "^4", "typescript": "^5"}}